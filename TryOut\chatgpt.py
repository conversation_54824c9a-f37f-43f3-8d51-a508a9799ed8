import json
filepath = input("Enter the file Path : ")

with open(filepath, 'r') as f:
    data = json.load(f)

# Extract and clean the 'code' key: remove only double newlines
clean_code = data['code'].replace('\n\n', '\n')
print(clean_code)

# with open('output.html', 'w', encoding='utf-8') as out:
#     out.write(clean_code)

# import json
# import sqlite3

# filepath = input("Enter the file Path : ")

# with open(filepath, 'r') as f:
#     data = json.load(f)

# # Extract and clean the 'code' key: remove only double newlines
# clean_code = data['code'].replace('\n\n', '\n')
# # print(clean_code)

# with open(r'C:\Myworkspace\python\agno\output.html', 'w', encoding='utf-8') as out:
#     out.write(clean_code)

# # Store the cleaned code in a SQLite database
# conn = sqlite3.connect('clean_code.db')
# cursor = conn.cursor()

# # Create table if it doesn't exist
# cursor.execute('''
#     CREATE TABLE IF NOT EXISTS code_storage (
#         id INTEGER PRIMARY KEY AUTOINCREMENT,
#         code TEXT
#     )
# ''')

# # Insert the cleaned code
# cursor.execute("INSERT INTO code_storage (code) VALUES (?)", (clean_code,))

# Commit the changes and close the connection
# conn.commit()
# conn.close()

# --- Extraction Code ---
# Connect to the database
# import sqlite3

# conn = sqlite3.connect('clean_code.db')
# cursor = conn.cursor()

# # Execute a query to retrieve all code from the table
# cursor.execute("SELECT code FROM code_storage")

# # Fetch all the results
# results = cursor.fetchall()

# # Close the connection
# conn.close()

# # Print the extracted code (or do something else with it)
# if results:
#     extracted_code = ""
#     for row in results:
#         extracted_code += row[0] + "\n"  # Access the 'code' column (index 0) and append to the string
# else:
#     extracted_code = None
#     print("No code found in the database.")


# # Example: Writing the extracted code to a new file

# with open('extracted_code.txt', 'w', encoding='utf-8') as outfile:
#     if extracted_code:
#       outfile.write(extracted_code) #write from string variable
#     else:
#       outfile.write("")
# print("\nExtracted code written to extracted_code.txt")
