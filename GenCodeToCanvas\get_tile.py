def get_tile_type_from_prompt(prompt: str) -> str:
    """
    Determines the tile_type based on keywords in the prompt.
    Returns one of: Python, Java, JavaScript, C#, C++, HTML, JSON, EXCEL, PPT, GRAPH.
    """
    prompt_lower = prompt.lower()
    if "excel" in prompt_lower:
        return "EXCEL"
    if "graph" in prompt_lower or "chart" in prompt_lower or "plot" in prompt_lower:
        return "GRAPH"
    if "ppt" in prompt_lower or "powerpoint" in prompt_lower or "presentation" in prompt_lower:
        return "PPT"
    if "python" in prompt_lower:
        return "Python"
    if "java " in prompt_lower or prompt_lower.startswith("java"):
        return "Java"
    if "javascript" in prompt_lower or "js" in prompt_lower:
        return "JavaScript"
    if "c++" in prompt_lower:
        return "C++"
    if "c#" in prompt_lower:
        return "C#"
    if "html" in prompt_lower:
        return "HTML"
    if "json" in prompt_lower:
        return "JSON"
    # Default fallback
    return "Python"

# Example usage:
prompt = '''Generate a Excel file with IPL 2025 points table till date. Sample Excel json format below : ...'''
tile_type = get_tile_type_from_prompt(prompt)
print(tile_type)  # Should print "EXCEL"
