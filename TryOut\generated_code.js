/**
 * Validates an email address using a regular expression.
 *
 * @param {string} email The email address to validate.
 * @returns {boolean} True if the email is valid, false otherwise.
 */
function isValidEmail(email) {
  // Regular expression for email validation.
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Test the email against the regular expression.
  return emailRegex.test(email);
}

// Example usage:
const email1 = "<EMAIL>";
const email2 = "invalid-email";
const email3 = "<EMAIL>";
const email4 = "<EMAIL>";
const email5 = "<EMAIL>";


console.log(`${email1} is valid: ${isValidEmail(email1)}`); // true
console.log(`${email2} is valid: ${isValidEmail(email2)}`); // false
console.log(`${email3} is valid: ${isValidEmail(email3)}`); // true
console.log(`${email4} is valid: ${isValidEmail(email4)}`); // true
console.log(`${email5} is valid: ${isValidEmail(email5)}`); // true