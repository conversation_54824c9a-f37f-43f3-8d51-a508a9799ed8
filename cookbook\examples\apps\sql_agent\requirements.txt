# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==0.1.2
    # via -r cookbook/examples/apps/sql/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anyio==4.8.0
    # via
    #   httpx
    #   openai
attrs==25.1.0
    # via
    #   jsonschema
    #   referencing
blinker==1.9.0
    # via streamlit
cachetools==5.5.1
    # via streamlit
certifi==2024.12.14
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   streamlit
    #   typer
distro==1.9.0
    # via openai
docstring-parser==0.16
    # via agno
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   agno
    #   streamlit
h11==0.14.0
    # via httpcore
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via
    #   agno
    #   openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jinja2==3.1.5
    # via
    #   altair
    #   pydeck
jiter==0.8.2
    # via openai
jsonschema==4.23.0
    # via altair
jsonschema-specifications==2024.10.1
    # via jsonschema
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
narwhals==1.24.0
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/apps/sql/requirements.in
numpy==2.2.2
    # via
    #   pandas
    #   pgvector
    #   pydeck
    #   streamlit
openai==1.60.1
    # via -r cookbook/examples/apps/sql/requirements.in
packaging==24.2
    # via
    #   altair
    #   streamlit
pandas==2.2.3
    # via
    #   -r cookbook/examples/apps/sql/requirements.in
    #   streamlit
pgvector==0.3.6
    # via -r cookbook/examples/apps/sql/requirements.in
pillow==11.1.0
    # via streamlit
protobuf==5.29.3
    # via streamlit
psycopg==3.2.4
    # via -r cookbook/examples/apps/sql/requirements.in
psycopg-binary==3.2.4
    # via psycopg
pyarrow==19.0.0
    # via streamlit
pydantic==2.10.6
    # via
    #   agno
    #   openai
    #   pydantic-settings
pydantic-core==2.27.2
    # via pydantic
pydantic-settings==2.7.1
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.1
    # via rich
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.0.1
    # via
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2024.2
    # via pandas
pyyaml==6.0.2
    # via agno
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via streamlit
rich==13.9.4
    # via
    #   agno
    #   streamlit
    #   typer
rpds-py==0.22.3
    # via
    #   jsonschema
    #   referencing
shellingham==1.5.4
    # via typer
simplejson==3.19.3
    # via -r cookbook/examples/apps/sql/requirements.in
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anyio
    #   openai
sqlalchemy==2.0.37
    # via -r cookbook/examples/apps/sql/requirements.in
streamlit==1.41.1
    # via -r cookbook/examples/apps/sql/requirements.in
tenacity==9.0.0
    # via streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.4.2
    # via streamlit
tqdm==4.67.1
    # via openai
typer==0.15.1
    # via agno
typing-extensions==4.12.2
    # via
    #   agno
    #   altair
    #   anyio
    #   openai
    #   psycopg
    #   pydantic
    #   pydantic-core
    #   referencing
    #   sqlalchemy
    #   streamlit
    #   typer
tzdata==2025.1
    # via pandas
urllib3==2.3.0
    # via requests
