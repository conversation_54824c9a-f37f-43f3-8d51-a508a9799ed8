## Description

- **Summary of changes**: Describe the key changes in this PR and their purpose.
- **Related issues**: Mention if this PR fixes or is connected to any issues.
- **Motivation and context**: Explain the reason for the changes and the problem they solve.
- **Environment or dependencies**: Specify any changes in dependencies or environment configurations required for this update.
- **Impact on metrics**: (If applicable) Describe changes in any metrics or performance benchmarks.

Fixes # (issue)

---

## Type of change

Please check the options that are relevant:

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Model update (Addition or modification of models)
- [ ] Other (please describe):

---

## Checklist

- [ ] Adherence to standards: Code complies with Agno’s style guidelines and best practices.
- [ ] Formatting and validation: You have run `./scripts/format.sh` and `./scripts/validate.sh` to ensure code is formatted and linted.
- [ ] Self-review completed: A thorough review has been performed by the contributor(s).
- [ ] Documentation: Docstrings and comments have been added or updated for any complex logic.
- [ ] Examples and guides: Relevant cookbook examples have been included or updated (if applicable).
- [ ] Tested in a clean environment: Changes have been tested in a clean environment to confirm expected behavior.
- [ ] Tests (optional): Tests have been added or updated to cover any new or changed functionality.

---

## Additional Notes

Include any deployment notes, performance implications, security considerations, or other relevant information (e.g., screenshots or logs if applicable).
