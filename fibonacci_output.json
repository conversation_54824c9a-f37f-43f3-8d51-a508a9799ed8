{"description": "Generates HTML and CSS to display a simple IPL 2025 points table.  The HTML defines the table structure, and the CSS provides basic styling.  The table is populated with placeholder data.", "language": "HTML/CSS", "code": "<!DOCTYPE html>\n<html>\n<head>\n<title>IPL 2025 Points Table</title>\n<style>\nbody {\n  font-family: Arial, sans-serif;\n  margin: 20px;\n}\n\ntable {\n  width: 100%;\n  border-collapse: collapse;\n  margin-bottom: 20px;\n}\n\nth, td {\n  border: 1px solid #ddd;\n  padding: 8px;\n  text-align: left;\n}\n\nth {\n  background-color: #f2f2f2;\n}\n\ntr:nth-child(even) {\n  background-color: #f9f9f9;\n}\n\n.highlight {\n  background-color: #ffffcc;\n}\n</style>\n</head>\n<body>\n\n<h1>IPL 2025 Points Table</h1>\n\n<table>\n  <thead>\n    <tr>\n      <th>Team</th>\n      <th>Played</th>\n      <th>Won</th>\n      <th>Lost</th>\n      <th>Tied</th>\n      <th>NR</th>\n      <th>Points</th>\n      <th>NRR</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td>Mumbai Indians</td>\n      <td>10</td>\n      <td>7</td>\n      <td>3</td>\n      <td>0</td>\n      <td>0</td>\n      <td>14</td>\n      <td>+0.500</td>\n    </tr>\n    <tr>\n      <td>Chennai Super Kings</td>\n      <td>10</td>\n      <td>6</td>\n      <td>4</td>\n      <td>0</td>\n      <td>0</td>\n      <td>12</td>\n      <td>+0.400</td>\n    </tr>\n    <tr>\n      <td>Royal Challengers Bangalore</td>\n      <td>10</td>\n      <td>6</td>\n      <td>4</td>\n      <td>0</td>\n      <td>0</td>\n      <td>12</td>\n      <td>+0.300</td>\n    </tr>\n    <tr>\n      <td>Kolkata Knight Riders</td>\n      <td>10</td>\n      <td>5</td>\n      <td>5</td>\n      <td>0</td>\n      <td>0</td>\n      <td>10</td>\n      <td>+0.200</td>\n    </tr>\n    <tr>\n      <td>Punjab Kings</td>\n      <td>10</td>\n      <td>5</td>\n      <td>5</td>\n      <td>0</td>\n      <td>0</td>\n      <td>10</td>\n      <td>-0.100</td>\n    </tr>\n    <tr>\n      <td>Rajasthan Royals</td>\n      <td>10</td>\n      <td>4</td>\n      <td>6</td>\n      <td>0</td>\n      <td>0</td>\n      <td>8</td>\n      <td>-0.200</td>\n    </tr>\n    <tr>\n      <td>Delhi Capitals</td>\n      <td>10</td>\n      <td>4</td>\n      <td>6</td>\n      <td>0</td>\n      <td>0</td>\n      <td>8</td>\n      <td>-0.300</td>\n    </tr>\n    <tr>\n      <td>Sunrisers Hyderabad</td>\n      <td>10</td>\n      <td>3</td>\n      <td>7</td>\n      <td>0</td>\n      <td>0</td>\n      <td>6</td>\n      <td>-0.400</td>\n    </tr>\n  </tbody>\n</table>\n\n</body>\n</html>", "dependencies": [], "example_usage": "Save the code as an HTML file (e.g., ipl_table.html) and open it in a web browser.  No additional dependencies are required."}