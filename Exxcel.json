{"description": "Generates an Excel file representing the IPL 2025 points table.", "language": "EXCEL", "code": "{ \"title\": \"IPL 2025 Points Table\", \"sheets\": [ { \"name\": \"Points Table\", \"headers\": [\"Team\", \"Matches Played\", \"Wins\", \"Losses\", \"Ties\", \"No Result\", \"Points\", \"Net Run Rate\"], \"data\": [ [\"Team A\", 5, 4, 1, 0, 0, 8, 1.25], [\"Team B\", 5, 3, 2, 0, 0, 6, 0.80], [\"Team C\", 5, 3, 2, 0, 0, 6, 0.35], [\"Team D\", 5, 2, 3, 0, 0, 4, -0.10], [\"Team E\", 5, 2, 3, 0, 0, 4, -0.50], [\"Team F\", 5, 1, 4, 0, 0, 2, -0.90] ] } ] }", "dependencies": [], "example_usage": "This JSON represents the data for an IPL 2025 points table.  It can be used with a library or tool that converts JSON to Excel format to generate the actual .xlsx file. For example, in Python, you could use the 'openpyxl' library."}