from bs4 import BeautifulSoup
import re

# Function to extract HTML from mixed text and ensure a valid HTML structure
def extract_html(mixed_text):
    try:
        soup = BeautifulSoup(mixed_text, 'html.parser')
        body_content = str(soup.body or soup)  # Extract content within <body> or the whole soup if <body> is missing

        # Ensure all tags are properly closed
        body_content = re.sub(r'<([^/][^>]*)>', r'<\1>', body_content)  # Re-apply opening tags
        body_content = re.sub(r'</([^>]*)>', r'</\1>', body_content)  # Re-apply closing tags

        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extracted HTML</title>
</head>
<body>
    {body_content}
</body>
</html>
"""
        return html_content
    except Exception as e:
        return f"<!-- Error extracting HTML: {str(e)} -->"

# Example mixed text message
mixed_text = """
Hello, this is a sample text message.


Sample HTML



This is a paragraph in HTML.

https://example.comThis is a link


Thank you!
"""

# Extract HTML from mixed text
html_content = extract_html(mixed_text)

# Save the extracted HTML content to a file
with open('extracted_html.html', 'w') as f:
    f.write(html_content)

print("HTML content extracted and saved to extracted_html.html")
