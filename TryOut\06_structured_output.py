"""🎬 Generic Code/File Generator - Your AI Partner for any Language/File Type

This example shows how to use structured outputs with AI agents to generate
code or files of any programming language or type (e.g., Python, HTML, XML, JSON).
It shows two approaches:
1. JSON Mode: Traditional JSON response parsing
2. Structured Output: Enhanced structured data handling

Example prompts to try:
- "Python script to calculate Fibonacci numbers"
- "HTML page with a simple form"
- "XML configuration file for a web server"
- "JSON data for a list of products"

Run `pip install openai agno` to install dependencies.
"""

from textwrap import dedent
from typing import List, Dict, Any

from agno.agent import Agent, RunResponse  # noqa
from agno.models.openai import OpenAIChat
from agno.models.google import Gemini
from agno.models.openrouter import OpenRouter
from agno.tools.duckduckgo import DuckDuckGoTools
from pydantic import BaseModel, Field
import json


class CodeFile(BaseModel):
    description: str = Field(
        ...,
        description="A detailed description of the code or file's purpose and functionality.",
    )
    language: str = Field(
        ...,
        description="The programming language or file type (e.g., Python, HTML, XML, EXCEL).",
    )
    code: str = Field(
        ...,
        description="The complete code or file content.",
    )
    dependencies: List[str] = Field(
        default_factory=list,
        description="A list of any dependencies required to run the code (e.g., libraries, packages).",
    )
    example_usage: str = Field(
        ...,
        description="An example of how to use the generated code or file.",
    )


# Agent that uses JSON mode
json_mode_agent = Agent(
    # model=OpenAIChat(id="gpt-4o"),
    model=Gemini(id="gemini-2.0-flash-exp"),
    # model=OpenRouter(id="gpt-4o"),
    description=dedent("""\
        You are an expert software developer capable of generating code or files
        of any programming language or type.
        You are able to understand complex requirements and generate clean,
        well-documented code that meets those requirements.\
    """),
    instructions=dedent("""\
        When generating code or files, follow these principles:

        1. Understand the requirements:
           - Make sure you fully understand the purpose and functionality of the code or file.
           - Ask clarifying questions if necessary.

        2. Choose the right language or file type:
           - Select the most appropriate language or file type for the task.
           - Consider factors such as performance, readability, and compatibility.

        3. Write clean, well-documented code:
           - Use meaningful variable names and comments.
           - Follow the coding conventions of the language or file type.
           - Make sure the code is easy to read and understand.

        4. Provide example usage:
           - Show how to use the generated code or file.
           - Include any necessary dependencies or configuration instructions.
        
        5. Search the web for any additional information needed to generate the code or file.

        6. Do not provide code explanations or improvements, just the code.\
    """),
    # tools=[DuckDuckGoTools()],
    markdown=True,
    show_tool_calls=True,
    response_model=CodeFile,
)

# Agent that uses structured outputs
structured_output_agent = Agent(
    # model=OpenAIChat(id="gpt-4o"),
    model=Gemini(id="gemini-2.0-flash-exp"),
    description=dedent("""\
        You are an expert software developer capable of generating code or files
        of any programming language or type.
        You are able to understand complex requirements and generate clean,
        well-documented code that meets those requirements.\
    """),
    instructions=dedent("""\
        When generating code or files, follow these principles:

        1. Understand the requirements:
           - Make sure you fully understand the purpose and functionality of the code or file.
           - Ask clarifying questions if necessary.

        2. Choose the right language or file type:
           - Select the most appropriate language or file type for the task.
           - Consider factors such as performance, readability, and compatibility.

        3. Write clean, well-documented code:
           - Use meaningful variable names and comments.
           - Follow the coding conventions of the language or file type.
           - Make sure the code is easy to read and understand.

        4. Provide example usage:
           - Show how to use the generated code or file.
           - Include any necessary dependencies or configuration instructions.\
    """),
    response_model=CodeFile,
    structured_outputs=True,
)

# Example usage with different languages/file types
# json_mode_agent.print_response("Python script to calculate Fibonacci numbers", stream=True)
# structured_output_agent.print_response("HTML page with a simple form", stream=True)

# More examples to try:
"""
Creative prompts to explore:
1. "XML configuration file for a web server"
2. "JSON data for a list of products"
3. "C++ code for a sorting algorithm"
4. "JavaScript code for a simple game"
5. "Dockerfile for a Python application"
"""

# To get the response in a variable:
# from rich.pretty import pprint

# json_mode_response: RunResponse = json_mode_agent.run("Python script to calculate Fibonacci numbers")
# pprint(json_mode_response.content)
# structured_output_response: RunResponse = structured_output_agent.run("HTML page with a simple form")
# pprint(structured_output_response.content)

jsonfile = json_mode_agent.run('''Generate a Excel file with IPL 2025 points table till date. Sample Excel json format below : 
    {
  "title": "Sales Report",
  "sheets": [
    {
      "name": "Q1 Sales",
      "headers": ["Product", "January", "February", "March", "Total"],
      "data": [
        ["Laptops", 42, 38, 55, 135],
        ["Smartphones", 78, 82, 91, 251],
        ["Tablets", 35, 29, 43, 107],
        ["Accessories", 120, 111, 129, 360]
      ]
    },
    {
      "name": "Q2 Sales",
      "headers": ["Product", "April", "May", "June", "Total"],
      "data": [
        ["Laptops", 51, 47, 63, 161],
        ["Smartphones", 85, 93, 102, 280],
        ["Tablets", 40, 38, 51, 129],
        ["Accessories", 132, 118, 145, 395]
      ]
    }
  ]
}
''')
with open("Exxcel.json", "w", encoding="utf-8") as f:
    f.write(jsonfile.content.model_dump_json(indent=2))

# htmlfile = structured_output_agent.run("HTML page with a simple form")
# with open("form_output.html", "w", encoding="utf-8") as f:
#     f.write(htmlfile.content.model_dump_json(indent=2))