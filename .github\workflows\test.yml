name: test

on:
  push:
  pull_request:
    types:
      - opened
      - edited
      - reopened
    branches:
      - 'main'

jobs:
  format-lint:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9"]

    defaults:
      run:
        working-directory: libs/agno

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade \
            pip setuptools wheel \
            ruff mypy pytest types-pyyaml
          pip install --no-deps -r requirements.txt
      - name: Ruff format
        run: |
          ruff format .
      - name: Ruff check
        run: |
          ruff check .
      - name: Mypy
        run: |
          mypy .
#      - name: Test
#        run: |
#          pytest
