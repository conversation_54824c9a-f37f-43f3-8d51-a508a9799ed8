from agno.tools.sql import SQLTools
import json

db_url = "postgresql+psycopg://ai:ai@localhost:5532/ai"

sql_tools = SQLTools(db_url=db_url)

# Create the table
sql_tools.execute("CREATE TABLE IF NOT EXISTS Data (Code TEXT);")

# Read the HTML data from ipl_table_output.json
with open("ipl_table_output.json", "r") as f:
    data = json.load(f)
    html_code = data["code"]

# Insert the HTML data into the table
sql_tools.execute(f"INSERT INTO Data (Code) VALUES ('{html_code}');")

print("Data inserted successfully!")
