"""🧠 Test Case Generator Expert with Knowledge - Your AI Test Case Generating Assistant!

This example shows how to create an AI test case generating assistant that combines knowledge from
various file types (PDF, TXT, DOCX) with web searching capabilities. The agent uses a knowledge base
created from the provided files and can supplement this information with web searches when needed.

Run `pip install openai lancedb tantivy pypdf python-docx duckduckgo-search agno` to install dependencies.
"""

import argparse
from textwrap import dedent
from pathlib import Path

from agno.agent import Agent
from agno.embedder.google import GeminiEmbedder
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader
from agno.knowledge.text import TextKnowledgeBase
from agno.knowledge.docx import DocxKnowledgeBase  # Assuming agno supports docx
from agno.models.google import Gemini
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.vectordb.lancedb import LanceDb


class CompositeKnowledgeBase:
    """A wrapper to combine multiple knowledge bases into one."""
    def __init__(self, knowledge_bases):
        self.knowledge_bases = knowledge_bases

    def search(self, query, num_documents=5, **kwargs):
        """Search across all knowledge bases and combine results."""
        results = []
        for kb in self.knowledge_bases:
            results.extend(kb.search(query=query, num_documents=num_documents, **kwargs))
        return results


# Function to initialize the appropriate knowledge base for each file
def initialize_knowledge_bases(file_paths):
    knowledge_bases = []

    for file_path in file_paths:
        if file_path.endswith(".pdf"):
            # Initialize PDFKnowledgeBase for PDF files
            knowledge_bases.append(
                PDFKnowledgeBase(
                    path=file_path,
                    vector_db=LanceDb(
                        uri="tmp/lancedb",
                        table_name="pdf_knowledge",
                        embedder=GeminiEmbedder(),
                    ),
                    reader=PDFReader(chunk=True),
                )
            )
        elif file_path.endswith(".txt"):
            # Initialize TextKnowledgeBase for text files
            knowledge_bases.append(
                TextKnowledgeBase(
                    path=file_path,
                    vector_db=LanceDb(
                        uri="tmp/lancedb",
                        table_name="text_knowledge",
                        embedder=GeminiEmbedder(),
                    ),
                )
            )
        elif file_path.endswith(".docx"):
            # Initialize DocxKnowledgeBase for DOCX files
            knowledge_bases.append(
                DocxKnowledgeBase(
                    path=file_path,
                    vector_db=LanceDb(
                        uri="tmp/lancedb",
                        table_name="docx_knowledge",
                        embedder=GeminiEmbedder(),
                    ),
                )
            )
        else:
            raise ValueError(f"Unsupported file type: {file_path}. Supported types are .pdf, .txt, .docx.")

    return CompositeKnowledgeBase(knowledge_bases)


if __name__ == "__main__":
    # Parse command-line arguments for multiple files
    parser = argparse.ArgumentParser(description="Generate test cases and BDD gherkin ouput from the given input files.")
    parser.add_argument("files", nargs="+", help="Paths to the input files.")
    args = parser.parse_args()

    file_paths = args.files

    # Initialize the composite knowledge base
    try:
        knowledge_base = initialize_knowledge_bases(file_paths)
    except ValueError as e:
        print(f"Error: {e}")
        exit(1)

    # Create the Test Case Generator Agent
    TestCaseGenAgent = Agent(
        model=Gemini(),
        instructions=dedent("""\
            You are an experienced and knowledgeable Automation Tester expert! 👩‍💼
            Think of yourself as a combination of a professional, enthusiastic automation developer.

            Follow these steps when answering questions:
            1. Analyze the provided knowledge base (PDF, text, or DOCX files).
            2. Generate test cases based on the provided information.
            3. Additionally, generate BDD Gherkin-style scenarios for the same functionality.
            4. Ensure the test cases and Gherkin scenarios are detailed and cover edge cases.

            Communication style:
            1. Structure your responses clearly:
                - Brief introduction or context
                - Main content (test cases and Gherkin scenarios)
                - Use tables for test cases and formatted Gherkin syntax for scenarios.
            2. Use friendly, encouraging language.

            Special features:
            - Explain unfamiliar test methods for the generated test cases.
            - Share relevant references if applicable.

            End each response with an uplifting sign-off like:
            - 'Happy Testing! Sayonara (Enjoy your Test)!'
        """),
        knowledge=knowledge_base,  # Use the composite knowledge base
        tools=[DuckDuckGoTools()],
        show_tool_calls=True,
        markdown=True,
        add_references=True,
    )

    # Load the knowledge bases
    for kb in knowledge_base.knowledge_bases:
        kb.load()

    # Generate test cases and BDD Gherkin scenarios
    TestCaseGenAgent.print_response(
        # "Please help to write test cases and BDD gherkin output",
        "Please help to write test cases and BDD Gherkin output for the given Sample all Requirements & use cases and please use the firmware upgrade procedure & usage understanding from the attached Generic Firmware upgrade Procedure.txt. ",
        stream=True,
    )