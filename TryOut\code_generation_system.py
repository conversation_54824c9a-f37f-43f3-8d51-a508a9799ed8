"""🎬 Advanced Code Generation System - Multi-format Output Generator

This system uses Agno to generate code in various formats (programming languages, 
markup, structured data) based on natural language prompts, and properly formats 
the results for appropriate UI rendering.

Example prompts to try:
- "Python script to calculate Fi<PERSON><PERSON>ci numbers"
- "HTML page with a simple form"
- "Excel spreadsheet showing IPL 2025 points table"
- "PowerPoint presentation about climate change"

Run `pip install openai agno pydantic` to install dependencies.
"""

from textwrap import dedent
from typing import List, Optional, Dict, Any, Literal
import json
import os

from agno.agent import Agent, RunResponse
from agno.models.openai import OpenAIChat
from agno.models.google import Gemini
from agno.tools.duckduckgo import DuckDuckGoTools
from pydantic import BaseModel, Field


class CodeGenerationResponse(BaseModel):
    description: str = Field(
        ...,
        description="A detailed description of the code or file's purpose and functionality.",
    )
    language: str = Field(
        ...,
        description="The programming language or file type (e.g., python, html, xml, excel, ppt).",
    )
    tile_type: str = Field(
        ...,
        description="UI tile type to use for rendering (e.g., Code Tile, HTML Tile, Excel Tile, PPT Tile).",
    )
    code: str = Field(
        ...,
        description="The complete code or file content, formatted appropriately for the language/file type.",
    )
    dependencies: List[str] = Field(
        default_factory=list,
        description="A list of any dependencies required to run the code (e.g., libraries, packages).",
    )
    example_usage: str = Field(
        ...,
        description="An example of how to use the generated code or file.",
    )


def determine_tile_type(language: str) -> str:
    """Determine the appropriate UI tile type based on the language/format."""
    language = language.lower()
    
    if language in ["excel", "spreadsheet", "csv"]:
        return "Excel Tile"
    elif language in ["ppt", "powerpoint", "presentation"]:
        return "PPT Tile"
    elif language in ["html", "css", "web"]:
        return "HTML Tile"
    elif language == "python":
        return "Python Tile"
    elif language in ["chart", "graph", "visualization", "plot"]:
        return "Graph Tile"
    else:
        return "Code Tile"


def create_code_generation_agent() -> Agent:
    """Create and configure the Agno agent for code generation."""
    return Agent(
        model=Gemini(id="gemini-2.0-flash-exp"),  # Can be replaced with OpenAIChat(id="gpt-4o")
        description=dedent("""\
            You are an expert software developer capable of generating code or files
            of any programming language or type, including structured data formats.
            You specialize in creating clean, well-documented code that meets specific requirements.
        """),
        instructions=dedent("""\
            Generate code or structured data based on the user's prompt. Follow these guidelines:

            1. Analyze the prompt to determine the required output format (programming language, markup, structured data)
            2. For standard code (Python, JavaScript, etc.):
               - Write clean, well-documented code with appropriate comments
               - Follow language conventions and best practices
            
            3. For Excel spreadsheets:
               - Format the code as a JSON structure with "title", "sheets", "headers", and "data" fields
               - Example: {"title": "Report", "sheets": [{"name": "Sheet1", "headers": ["Col1", "Col2"], "data": [[1, 2], [3, 4]]}]}
            
            4. For PowerPoint presentations:
               - Format the code as HTML-like markup with <div class="slide"> elements
               - Include slide titles, headings, and content
            
            5. For all outputs:
               - Provide a clear description of the generated content
               - List any dependencies required
               - Include example usage instructions
               - Specify the correct language/file type
               - Determine the appropriate UI tile type for rendering
            
            Always return a complete, properly structured response that follows the CodeGenerationResponse model.
        """),
        # tools=[DuckDuckGoTools()],
        markdown=True,
        show_tool_calls=True,
        response_model=CodeGenerationResponse,
        structured_outputs=True,
    )


def generate_code(prompt: str) -> Dict[str, Any]:
    """
    Generate code based on the given prompt and return a standardized response.
    
    Args:
        prompt: Natural language prompt describing the code to be generated
        
    Returns:
        Standardized JSON object with the generated code and metadata
    """
    agent = create_code_generation_agent()
    response: RunResponse = agent.run(prompt)
    
    # Extract the structured output
    result = response.content.model_dump()
    
    # Ensure the tile_type is set correctly based on the language
    if "tile_type" not in result or not result["tile_type"]:
        result["tile_type"] = determine_tile_type(result["language"])
    
    return result


def save_generated_code(result: Dict[str, Any], output_path: str = None) -> str:
    """
    Save the generated code to a file with an appropriate extension.
    
    Args:
        result: The generated code result
        output_path: Optional path to save the file
        
    Returns:
        Path to the saved file
    """
    language = result["language"].lower()
    
    # Determine file extension
    extension_map = {
        "python": "py",
        "javascript": "js",
        "html": "html",
        "css": "css",
        "excel": "json",
        "ppt": "html",
        "json": "json",
        "xml": "xml",
    }
    
    extension = extension_map.get(language, "txt")
    
    # Create filename
    if output_path:
        filename = output_path
    else:
        base_name = "generated_" + language
        filename = f"{base_name}.{extension}"
    
    # Save the code to file
    with open(filename, "w", encoding="utf-8") as f:
        if language == "excel":
            # For Excel, save the structured JSON
            f.write(result["code"])
        else:
            # For other formats, save the code directly
            f.write(result["code"])
    
    return filename


def process_prompt(prompt: str, save_output: bool = True) -> Dict[str, Any]:
    """
    Process a natural language prompt to generate code and prepare it for UI rendering.
    
    Args:
        prompt: Natural language prompt describing the code to be generated
        save_output: Whether to save the generated code to a file
        
    Returns:
        Processed result ready for UI rendering
    """
    # Generate code based on the prompt
    result = generate_code(prompt)
    
    # Save the generated code if requested
    if save_output:
        output_path = save_generated_code(result)
        result["file_path"] = output_path
    
    return result


# Example usage
if __name__ == "__main__":
    # Test with different types of prompts
    test_prompts = [
        "Python script to calculate Fibonacci numbers",
        "HTML page with a simple form",
        "Excel spreadsheet showing IPL 2025 points table with team names, matches played, won, lost, points",
        "PowerPoint presentation with 3 slides about climate change"
    ]
    
    for prompt in test_prompts:
        print(f"\n\n{'='*80}\nProcessing prompt: {prompt}\n{'='*80}")
        result = process_prompt(prompt)
        
        # Print a summary of the result
        print(f"\nGenerated {result['language']} content for {result['tile_type']}")
        print(f"Description: {result['description']}")
        print(f"Dependencies: {', '.join(result['dependencies'])}")
        print(f"Saved to: {result.get('file_path', 'Not saved')}")
        
        # Print a preview of the code (first few lines)
        code_preview = result['code'].split('\n')[:5]
        print(f"\nCode preview:\n{'-'*40}")
        for line in code_preview:
            print(line)
        print(f"{'-'*40}\n... (truncated)")


"""
API Integration module for the Code Generation System.
This module handles the integration between the code generation system and UI rendering.
"""

import json
from typing import Dict, Any

from code_generation_system import process_prompt


def render_to_appropriate_tile(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare the generated code for rendering in the appropriate UI tile.
    
    Args:
        result: The processed result from the code generation system
        
    Returns:
        Data formatted for the specific tile type
    """
    tile_type = result["tile_type"]
    language = result["language"].lower()
    
    # Format data based on tile type
    if tile_type == "Excel Tile":
        # For Excel, parse the JSON structure if it's a string
        if isinstance(result["code"], str):
            try:
                excel_data = json.loads(result["code"])
            except json.JSONDecodeError:
                # If not valid JSON, return as is
                excel_data = {"error": "Invalid Excel data format", "raw": result["code"]}
            return {
                "type": "excel",
                "data": excel_data,
                "metadata": {
                    "description": result["description"],
                    "dependencies": result["dependencies"],
                    "example_usage": result["example_usage"]
                }
            }
    
    elif tile_type == "PPT Tile":
        return {
            "type": "ppt",
            "data": result["code"],
            "metadata": {
                "description": result["description"],
                "dependencies": result["dependencies"],
                "example_usage": result["example_usage"]
            }
        }
    
    elif tile_type == "HTML Tile":
        return {
            "type": "html",
            "data": result["code"],
            "metadata": {
                "description": result["description"],
                "dependencies": result["dependencies"],
                "example_usage": result["example_usage"]
            }
        }
    
    elif tile_type == "Graph Tile":
        return {
            "type": "graph",
            "data": result["code"],
            "metadata": {
                "description": result["description"],
                "dependencies": result["dependencies"],
                "example_usage": result["example_usage"]
            }
        }
    
    else:  # Default Code Tile
        return {
            "type": "code",
            "data": result["code"],
            "language": language,
            "metadata": {
                "description": result["description"],
                "dependencies": result["dependencies"],
                "example_usage": result["example_usage"]
            }
        }


def generate_and_render(prompt: str) -> Dict[str, Any]:
    """
    Generate code from a prompt and prepare it for UI rendering.
    
    Args:
        prompt: Natural language prompt describing the code to be generated
        
    Returns:
        Data formatted for the appropriate UI tile
    """
    # Process the prompt to generate code
    result = process_prompt(prompt)
    
    # Format the result for the appropriate tile
    formatted_result = render_to_appropriate_tile(result)
    
    return formatted_result


def invoke_appropriate_tile(formatted_result: Dict[str, Any]) -> None:
    """
    Invoke the appropriate tile function based on the formatted result.
    
    Args:
        formatted_result: The formatted result from render_to_appropriate_tile
    """
    tile_type = formatted_result["type"]
    
    # Import tile functions here to avoid circular imports
    from canvas_tiles import (
        create_code_tile, 
        create_excel_tile, 
        create_ppt_tile, 
        create_html_tile, 
        create_graph_tile
    )
    
    # Invoke the appropriate tile function
    if tile_type == "excel":
        # For Excel tiles, pass the structured data
        create_excel_tile(
            data=formatted_result["data"],
            title=formatted_result["data"].get("title", "Excel Data"),
            description=formatted_result["metadata"]["description"],
            dependencies=formatted_result["metadata"]["dependencies"],
            example_usage=formatted_result["metadata"]["example_usage"]
        )
    
    elif tile_type == "ppt":
        # For PowerPoint tiles, pass the HTML-like markup
        create_ppt_tile(
            content=formatted_result["data"],
            description=formatted_result["metadata"]["description"],
            dependencies=formatted_result["metadata"]["dependencies"],
            example_usage=formatted_result["metadata"]["example_usage"]
        )
    
    elif tile_type == "html":
        # For HTML tiles, pass the HTML content
        create_html_tile(
            content=formatted_result["data"],
            description=formatted_result["metadata"]["description"],
            dependencies=formatted_result["metadata"]["dependencies"],
            example_usage=formatted_result["metadata"]["example_usage"]
        )
    
    elif tile_type == "graph":
        # For Graph tiles, pass the graph data
        create_graph_tile(
            data=formatted_result["data"],
            description=formatted_result["metadata"]["description"],
            dependencies=formatted_result["metadata"]["dependencies"],
            example_usage=formatted_result["metadata"]["example_usage"]
        )
    
    else:  # Default Code Tile
        # For Code tiles, pass the code and language
        create_code_tile(
            code=formatted_result["data"],
            language=formatted_result["language"],
            description=formatted_result["metadata"]["description"],
            dependencies=formatted_result["metadata"]["dependencies"],
            example_usage=formatted_result["metadata"]["example_usage"]
        )


def process_and_render_to_canvas(prompt: str) -> None:
    """
    Process a prompt, generate code, and render it to the appropriate canvas tile.
    
    This is the main function to be called from the UI to generate and display code.
    
    Args:
        prompt: Natural language prompt describing the code to be generated
    """
    # Generate and format the code
    formatted_result = generate_and_render(prompt)
    
    # Invoke the appropriate tile function
    invoke_appropriate_tile(formatted_result)
    
    return formatted_result


# Example usage
if __name__ == "__main__":
    # Test with different types of prompts
    test_prompts = [
        "Python script to calculate Fibonacci numbers",
        "HTML page with a simple form",
        "Excel spreadsheet showing IPL 2025 points table with team names, matches played, won, lost, points",
        "PowerPoint presentation with 3 slides about climate change"
    ]
    
    for prompt in test_prompts:
        print(f"\n\n{'='*80}\nTesting prompt: {prompt}\n{'='*80}")
        result = generate_and_render(prompt)
        print(f"Tile Type: {result['type']}")
        
        # Print a preview of the data
        if result['type'] == 'excel':
            print(f"Excel Title: {result['data'].get('title', 'N/A')}")
            print(f"Sheets: {len(result['data'].get('sheets', []))}")
        elif result['type'] in ['html', 'ppt']:
            preview = result['data'][:200] + "..." if len(result['data']) > 200 else result['data']
            print(f"Content Preview: {preview}")
        else:
            lines = result['data'].split('\n')[:5]
            preview = '\n'.join(lines) + "\n..." if len(lines) > 5 else result['data']
            print(f"Code Preview:\n{preview}")
