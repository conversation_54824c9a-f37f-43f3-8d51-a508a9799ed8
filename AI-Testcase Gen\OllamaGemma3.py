"""🧠 Test Case and BDD Gherkin Generator!

This script generates test cases and BDD Gherkin output based on a requirements PDF file and a firmware upgrade procedure text file.

To run:
python Try.py

Run `pip install openai lancedb tantivy pypdf duckduckgo-search agno ollama` to install dependencies.
"""

from textwrap import dedent

from agno.agent import Agent
from agno.embedder.ollama import OllamaEmbedder
from agno.knowledge.pdf import PDFKnowledgeBase
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.models.ollama import Ollama

from agno.vectordb.lancedb import LanceDb, SearchType

def main():
    requirements_file = r"C:\Users\<USER>\Downloads\Generic Sample requirements for GenAI Demo.docx"
    firmware_file = r"C:\Users\<USER>\Downloads\Generic - Alpha Firmware upgrade.txt"
    output_file = "output.txt"

    with open(firmware_file, "r") as f:
        firmware_procedure = f.read()

    # Create an Agent for test case generation
    agent = Agent(
        model=Ollama(id="llama3.2:latest"),
        instructions=dedent("""\
            You are an expert test case and BDD Gherkin output generator.
            You will receive a PDF document containing requirements and a text file containing firmware upgrade procedures. Your task is to generate test cases and BDD Gherkin output based on the content of both files.

            Follow these steps when generating test cases and BDD Gherkin output:
            1.  Read and understand the content of the PDF document and the text file.
            2.  Identify the key functionalities, features, and requirements described in the PDF and the firmware upgrade procedures in the text file.
            3.  Generate a comprehensive set of test cases that cover various aspects of the system or product described in the PDF and the firmware upgrade procedures.
            4.  Each test case should include a test case ID, a description of the test scenario, input data that must be entered or verified (if applicable), and expected results in a tabular format.
            5.  Prioritize test cases based on their importance and potential impact.
            6.  If information is not found in the PDF or the text file, search the web for relevant information.
            7.  Ensure that the test cases are clear, concise, and easy to understand.
            8.  Consider different test levels (e.g., unit, integration, system) and test types (e.g., functional, performance, security).
            9.  Include both positive and negative test cases to validate the system's behavior under different conditions.
            10. Generate BDD Gherkin output for each test case, following the Given-When-Then format.
            11. Organize the test cases and BDD Gherkin output in a structured format (e.g., a table or a list).
            12. Provide a rationale for each test case, explaining why it is important and what it aims to achieve.

            Communication style:
            1. Be clear, concise, and professional in your responses.
            2. Use a structured format for presenting the test cases and BDD Gherkin output.
            3. Provide a rationale for each test case.

            Remember:
            - Always refer to the PDF document and the text file for accurate information.
            - Ensure that the test cases are comprehensive and cover all relevant aspects of the system and the firmware upgrade procedures.
            - Prioritize test cases based on their importance and potential impact.\
        """),
        knowledge=PDFKnowledgeBase(
            path=requirements_file,
            vector_db=LanceDb(
                uri="tmp/lancedb",
                table_name="testing_knowledge",
                search_type=SearchType.hybrid,
                embedder=OllamaEmbedder(),
                use_tantivy=False
            ),
        ),
        tools=[DuckDuckGoTools()],
        show_tool_calls=True,
        markdown=True,
        add_references=True,
    )

    prompt = f"Please help to write test cases and BDD Gherkin output for the given Sample all requirements & use cases and please use the firmware upgrade procedure & usage understanding from the following: {firmware_procedure}"

    response = agent.print_response(prompt, stream=True)

    if response is not None:
        with open(output_file, "w") as output_file:
            output_file.write(str(response))
    else:
        print("Error: The agent returned a None response.")

if __name__ == "__main__":
    main()