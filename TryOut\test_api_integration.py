"""
API Integration module for the Code Generation System.
This module handles the integration between the code generation system and UI rendering.
"""

import json
from typing import Dict, Any

from code_generation_system import process_prompt


def render_to_appropriate_tile(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare the generated code for rendering in the appropriate UI tile.
    
    Args:
        result: The processed result from the code generation system
        
    Returns:
        Data formatted for the specific tile type
    """
    tile_type = result["tile_type"]
    language = result["language"].lower()
    
    # Format data based on tile type
    if tile_type == "Excel Tile":
        # For Excel, parse the JSON structure if it's a string
        if isinstance(result["code"], str):
            try:
                excel_data = json.loads(result["code"])
            except json.JSONDecodeError:
                # If not valid JSON, return as is
                excel_data = {"error": "Invalid Excel data format", "raw": result["code"]}
            return {
                "type": "excel",
                "data": excel_data,
                "metadata": {
                    "description": result["description"],
                    "dependencies": result["dependencies"],
                    "example_usage": result["example_usage"]
                }
            }
    
    elif tile_type == "PPT Tile":
        return {
            "type": "ppt",
            "data": result["code"],
            "metadata": {
                "description": result["description"],
                "dependencies": result["dependencies"],
                "example_usage": result["example_usage"]
            }
        }
    
    elif tile_type == "HTML Tile":
        return {
            "type": "html",
            "data": result["code"],
            "metadata": {
                "description": result["description"],
                "dependencies": result["dependencies"],
                "example_usage": result["example_usage"]
            }
        }
    
    elif tile_type == "Graph Tile":
        return {
            "type": "graph",
            "data": result["code"],
            "metadata": {
                "description": result["description"],
                "dependencies": result["dependencies"],
                "example_usage": result["example_usage"]
            }
        }
    
    else:  # Default Code Tile
        return {
            "type": "code",
            "data": result["code"],
            "language": language,
            "metadata": {
                "description": result["description"],
                "dependencies": result["dependencies"],
                "example_usage": result["example_usage"]
            }
        }


def generate_and_render(prompt: str) -> Dict[str, Any]:
    """
    Generate code from a prompt and prepare it for UI rendering.
    
    Args:
        prompt: Natural language prompt describing the code to be generated
        
    Returns:
        Data formatted for the appropriate UI tile
    """
    # Process the prompt to generate code
    result = process_prompt(prompt)
    
    # Format the result for the appropriate tile
    formatted_result = render_to_appropriate_tile(result)
    
    return formatted_result


# Example usage
if __name__ == "__main__":
    # Test with different types of prompts
    test_prompts = [
        "Python script to calculate Fibonacci numbers",
        "HTML page with a simple form",
        "Excel spreadsheet showing IPL 2025 points table with team names, matches played, won, lost, points",
        "PowerPoint presentation with 3 slides about climate change"
    ]
    
    for prompt in test_prompts:
        print(f"\n\n{'='*80}\nTesting prompt: {prompt}\n{'='*80}")
        result = generate_and_render(prompt)
        print(f"Tile Type: {result['type']}")
        
        # Print a preview of the data
        if result['type'] == 'excel':
            print(f"Excel Title: {result['data'].get('title', 'N/A')}")
            print(f"Sheets: {len(result['data'].get('sheets', []))}")
        elif result['type'] in ['html', 'ppt']:
            preview = result['data'][:200] + "..." if len(result['data']) > 200 else result['data']
            print(f"Content Preview: {preview}")
        else:
            lines = result['data'].split('\n')[:5]
            preview = '\n'.join(lines) + "\n..." if len(lines) > 5 else result['data']
            print(f"Code Preview:\n{preview}")