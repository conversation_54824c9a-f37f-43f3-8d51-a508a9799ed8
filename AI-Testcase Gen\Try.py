import chardet

files = [r"C:\Users\<USER>\Downloads\Generic Sample requirements for GenAI Demo.docx" , r"C:\Users\<USER>\Downloads\Generic - Alpha Firmware upgrade.txt"]
              
for file in files:
    with open(file, "rb") as f:
        raw_data = f.read()
        detected_encoding = chardet.detect(raw_data)["encoding"]
        # logger.info(f"Detected encoding for {file}: {detected_encoding}")
        print(f"Detected encoding for {file}: {detected_encoding}")
