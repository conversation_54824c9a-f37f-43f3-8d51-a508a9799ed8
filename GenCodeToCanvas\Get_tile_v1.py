"""🎬 Generic Code/File Generator - Your AI Partner for any Language/File Type

This example shows how to use structured outputs with AI agents to generate
code or files of any programming language or type (e.g., Python, HTML, XML, JSON).
It shows two approaches:
1. JSON Mode: Traditional JSON response parsing
2. Structured Output: Enhanced structured data handling

Example prompts to try:
- "Python script to calculate Fibonacci numbers"
- "HTML page with a simple form"
- "XML configuration file for a web server"
- "JSON data for a list of products"

Run `pip install openai agno` to install dependencies.
"""

from textwrap import dedent
from typing import List, Dict, Any

from agno.agent import Agent, RunResponse  # noqa
from agno.models.openai import OpenAIChat
from agno.models.google import Gemini
from agno.models.openrouter import OpenRouter
from agno.tools.duckduckgo import DuckDuckGoTools
from pydantic import BaseModel, Field
import json


class CodeFile(BaseModel):
    description: str = Field(
        ...,
        description="A detailed description of the code or file's purpose and functionality.",
    )
    language: str = Field(
        ...,
        description="The programming language or file type (e.g., Python, HTML, XML, EXCEL).",
    )
    code: str = Field(
        ...,
        description="The complete code or file content.",
    )
    tile_type: str = Field(
        ...,
        description="The type of tile to use for rendering (e.g., Python, Java, JavaScript, C#, C++, HTML, JSON, EXCEL, PPT, GRAPH).",
    )
    dependencies: List[str] = Field(
        default_factory=list,
        description="A list of any dependencies required to run the code (e.g., libraries, packages).",
    )
    example_usage: str = Field(
        ...,
        description="An example of how to use the generated code or file.",
    )


def get_tile_type_from_prompt(prompt: str) -> str:
    """
    Determines the tile_type based on keywords in the prompt.
    Returns one of: Python, Java, JavaScript, C#, C++, HTML, JSON, EXCEL, PPT, GRAPH.
    """
    prompt_lower = prompt.lower()
    if "excel" in prompt_lower:
        return "EXCEL"
    if "graph" in prompt_lower or "chart" in prompt_lower or "plot" in prompt_lower:
        return "GRAPH"
    if "ppt" in prompt_lower or "powerpoint" in prompt_lower or "presentation" in prompt_lower:
        return "PPT"
    if "python" in prompt_lower:
        return "Python"
    if "java " in prompt_lower or prompt_lower.startswith("java"):
        return "Java"
    if "javascript" in prompt_lower or "js" in prompt_lower:
        return "JavaScript"
    if "c++" in prompt_lower:
        return "C++"
    if "c#" in prompt_lower:
        return "C#"
    if "html" in prompt_lower:
        return "HTML"
    if "json" in prompt_lower:
        return "JSON"
    # Default fallback
    return "Python"


# Agent that uses JSON mode
json_mode_agent = Agent(
    # model=OpenAIChat(id="gpt-4o"),
    model=Gemini(id="gemini-2.0-flash-exp"),
    # model=OpenRouter(id="gpt-4o"),
    description=dedent("""\
        You are an expert software developer capable of generating code or files
        of any programming language or type.
        You are able to understand complex requirements and generate clean,
        well-documented code that meets those requirements.\
    """),
    instructions=dedent("""\
        When generating code or files, follow these principles:

        1. Understand the requirements:
           - Make sure you fully understand the purpose and functionality of the code or file.
           - Ask clarifying questions if necessary.

        2. Choose the right language or file type:
           - Select the most appropriate language or file type for the task.
           - Consider factors such as performance, readability, and compatibility.

        3. Write clean, well-documented code:
           - Use meaningful variable names and comments.
           - Follow the coding conventions of the language or file type.
           - Make sure the code is easy to read and understand.

        4. Provide example usage:
           - Show how to use the generated code or file.
           - Include any necessary dependencies or configuration instructions.
        
        5. Search the web for any additional information needed to generate the code or file.

        6. Do not provide code explanations or improvements, just the code.
        
        7. Set the tile_type field based on the type of code being generated:
           - For Python, Java, JavaScript, C#, C++, HTML, or JSON code, use the language name.
           - For Excel spreadsheets or data, use "EXCEL" (even though the code is in JSON format).
           - For graphs or charts, use "GRAPH" (even though the code is in JSON format).
           - For PowerPoint presentations, use "PPT" (even though the code is in HTML format).\
    """),
    # tools=[DuckDuckGoTools()],
    markdown=True,
    show_tool_calls=True,
    response_model=CodeFile,
)

# Process the user's prompt
def process_prompt(prompt: str):
    # Determine the tile type from the prompt
    tile_type = get_tile_type_from_prompt(prompt)
    
    # Run the agent to generate the code
    response = json_mode_agent.run(prompt)
    
    # Ensure the tile_type is set correctly
    if hasattr(response.content, 'tile_type') and not response.content.tile_type:
        response.content.tile_type = tile_type
    
    return response

jsonfile = process_prompt('''Generate a Excel file with IPL 2025 points table till date. Sample Excel json format below : 
    {
  "title": "Sales Report",
  "sheets": [
    {
      "name": "Q1 Sales",
      "headers": ["Product", "January", "February", "March", "Total"],
      "data": [
        ["Laptops", 42, 38, 55, 135],
        ["Smartphones", 78, 82, 91, 251],
        ["Tablets", 35, 29, 43, 107],
        ["Accessories", 120, 111, 129, 360]
      ]
    },
    {
      "name": "Q2 Sales",
      "headers": ["Product", "April", "May", "June", "Total"],
      "data": [
        ["Laptops", 51, 47, 63, 161],
        ["Smartphones", 85, 93, 102, 280],
        ["Tablets", 40, 38, 51, 129],
        ["Accessories", 132, 118, 145, 395]
      ]
    }
  ]
}
''')
with open("Exxcel.json", "w", encoding="utf-8") as f:
    f.write(jsonfile.content.model_dump_json(indent=2))
