'''🌟 Code Generation Agent - Multi-Language Code Creator

This example demonstrates how to create an AI agent capable of generating code in any language or file format.
The agent combines coding expertise with creative code generation to meet diverse user needs.
It showcases how personality and style instructions can shape an agent's responses.

Example prompts to try:
- "Generate an HTML page with a title 'My Portfolio'"
- "Write CSS code to style a navigation bar"
- "Create XML code for a library catalog"
- "Generate JSON data for a product inventory"
- "Write a Python script to find the largest number in a list"
'''

from textwrap import dedent

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.google import Gemini
from agno.tools.duckduckgo import DuckDuckGoTools

# Create a coding agent
agent = Agent(
    # model=OpenAIChat(id="gpt-4o"),
    model=Gemini(id="gemini-2.0-flash-exp"),
    instructions=dedent("""\
        You are a versatile coding assistant capable of generating code in any programming language or file format.
        
        Your responsibilities include:
        - Writing clean, well-documented code
        - Supporting languages like Python, JavaScript, Java, C++, and more
        - Generating structured files like HTML, XML, JSON, YAML, etc.
        - Providing concise explanations or comments when needed
        - Following best practices and adhering to language-specific conventions
        - Do not include the code explanations

        Always ensure the generated code is syntactically correct and ready to use.
        Remember: Add extra information from the web and always verify facts through web searches.\
    """),
    tools=[DuckDuckGoTools()],
    show_tool_calls=True,
    markdown=True,
)

# Example usage
agent.print_response(
    # "Generate a Python function to calculate the factorial of a number.", stream=True
    "Generate a HTML with CSS scripts to display the IPL 2025 points table till date", stream=True
)

# response = agent.run("Generate an HTML of the IPL 2025 points table till date")

# # Save the response to a file
# with open("ipl_2025_points_table.html", "w", encoding="utf-8") as file:
#     file.write(response.content)

# Example prompts
'''
Try these scenarios:
1. "Create an HTML template for a personal portfolio website."
2. "Write a JSON schema for a user profile."
3. "Generate a JavaScript function to validate email addresses."
4. "Create an XML file representing a book catalog."
5. "Write a C++ program to implement a basic calculator."
'''
