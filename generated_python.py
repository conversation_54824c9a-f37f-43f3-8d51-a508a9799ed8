# Function to calculate the nth Fibonacci number recursively
def fi<PERSON><PERSON>ci(n):
    # Check if the input is a non-negative integer
    if not isinstance(n, int) or n < 0:
        raise ValueError("Input must be a non-negative integer.")
    
    # Base cases
    if n <= 1:
        return n
    
    # Recursive step
    return fibon<PERSON>ci(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)

# Example usage
if __name__ == "__main__":
    try:
        num_terms = int(input("Enter the number of Fibonacci terms to calculate: "))
        
        # Validate the input
        if num_terms < 0:
            raise ValueError("Number of terms must be non-negative.")
        
        print("Fibonacci sequence:")
        for i in range(num_terms):
            print(fibonacci(i), end=" ")
        print()
            
    except ValueError as e:
        print(f"Error: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")