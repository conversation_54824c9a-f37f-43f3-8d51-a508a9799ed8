#include <iostream>

using namespace std;

int main() {
  char operation;
  double num1, num2, result;

  cout << "Enter operator (+, -, *, /): ";
  cin >> operation;

  cout << "Enter first number: ";
  cin >> num1;

  cout << "Enter second number: ";
  cin >> num2;

  switch (operation) {
    case '+':
      result = num1 + num2;
      cout << num1 << " + " << num2 << " = " << result << endl;
      break;
    case '-':
      result = num1 - num2;
      cout << num1 << " - " << num2 << " = " << result << endl;
      break;
    case '*':
      result = num1 * num2;
      cout << num1 << " * " << num2 << " = " << result << endl;
      break;
    case '/':
      if (num2 == 0) {
        cout << "Error! Division by zero is not allowed." << endl;
      } else {
        result = num1 / num2;
        cout << num1 << " / " << num2 << " = " << result << endl;
      }
      break;
    default:
      cout << "Error! Invalid operator." << endl;
  }

  return 0;
}


    g++ calculator.cpp -o calculator
    

    ./calculator