# from google import genai
# from google.genai import types
# import os

# client = genai.Client(api_key=os.getenv('GOOGLE_API_KEY'))

# response = client.models.generate_content(
#     model="gemini-2.5-flash-preview-04-17",
#     config=types.GenerateContentConfig(
#         system_instruction='''
#         You are a versatile coding assistant capable of generating code in any programming language or file format.

#         Your responsibilities include:
#         - Writing clean, well-documented code
#         - Supporting languages like Python, JavaScript, Java, C++, and more
#         - Generating structured files like HTML, XML, JSON, YAML, etc.
#         - Providing concise explanations or comments when needed
#         - Following best practices and adhering to language-specific conventions
#         - Do not include the code explanations
#         ''',
# ),
#     contents=["Generate a HTML with CSS scripts to display the IPL 2025 points table till date"]
# )
# print(response.text)

from google import genai
from google.genai import types
import os

client = genai.Client(api_key=os.getenv('GOOGLE_API_KEY'))

response = client.models.generate_content(
    model="gemini-2.5-flash-preview-04-17",
    config=types.GenerateContentConfig(
        system_instruction='''
        You are a versatile coding assistant capable of generating code in any programming language or file format.
        Your responsibilities include:
        - Writing clean, well-documented code
        - Supporting languages like Python, JavaScript, Java, C++, and more
        - Generating structured files like HTML, XML, JSON, YAML, etc.
        - Providing concise explanations or comments when needed
        - Following best practices and adhering to language-specific conventions
        - Do not include the code explanations
        ''',
        response_mime_type="application/json"
    ),
    contents=["Generate a HTML with CSS scripts to display the IPL 2025 points table till date"]
)
print(response.text)

# from google import genai
# from google.genai import types
# from pydantic import BaseModel
# import os

# class Recipe(BaseModel):
#     recipe_name: str
#     ingredients: list[str]

# client = genai.Client(api_key=os.getenv('GOOGLE_API_KEY'))
# response = client.models.generate_content(
#     model="gemini-2.0-flash",
#     config={
#         "response_mime_type": "application/html",
#         "response_schema": list[Recipe]
#     },
#     contents='''You are a versatile coding assistant capable of generating code in any programming language or file format.

#         Your responsibilities include:
#         - Writing clean, well-documented code
#         - Supporting languages like Python, JavaScript, Java, C++, and more
#         - Generating structured files like HTML, XML, JSON, YAML, etc.
#         - Providing concise explanations or comments when needed
#         - Following best practices and adhering to language-specific conventions
#         - Do not include the code explanations
#         Generate a HTML to display the IPL 2025 points table till date''',
# )
# # Use the response as a JSON string.
# print(response.text)

# # Use instantiated objects.
# my_recipes: list[Recipe] = response.parsed
