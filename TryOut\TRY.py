# import re
# from textwrap import dedent
# from agno.agent import Agent
# from agno.models.google import Gemini

# # Simple keyword-based extension map
# extension_map = {
#     "python": ".py",
#     "javascript": ".js",
#     "java": ".java",
#     "c++": ".cpp",
#     "html": ".html",
#     "css": ".css",
#     "json": ".json",
#     "xml": ".xml",
#     "yaml": ".yaml",
#     "markdown": ".md",
#     "bash": ".sh",
# }

# def get_extension(prompt: str) -> str:
#     prompt = prompt.lower()
#     for keyword, ext in extension_map.items():
#         if keyword in prompt:
#             return ext
#     return ".txt"  # default fallback

# # Create the agent
# agent = Agent(
#     model=Gemini(id="gemini-2.0-flash-exp"),
#     instructions=dedent("""\
#         You are a versatile coding assistant capable of generating code in any programming language or file format.

#         Your responsibilities include:
#         - Writing clean, well-documented code
#         - Supporting languages like Python, JavaScript, Java, C++, and more
#         - Generating structured files like HTML, XML, JSON, YAML, etc.
#         - Providing concise explanations or comments when needed
#         - Following best practices and adhering to language-specific conventions

#         Always ensure the generated code is syntactically correct and ready to use.\
#     """),
#     markdown=True,
# )

# # Prompt from user
# prompt = "Generate a python code snippet to calculate the area of a rectangle"

# # Get agent's response
# response = agent.run(prompt)

# # Determine file extension
# ext = get_extension(prompt)
# filename = f"generated_code{ext}"

# # Save to file
# with open(filename, "w", encoding="utf-8") as f:
#     f.write(response.content)

# print(f"✅ Response saved to {filename}")

import re
from textwrap import dedent
from agno.agent import Agent
from agno.models.google import Gemini
from agno.tools.duckduckgo import DuckDuckGoTools

# Language keyword to extension mapping
extension_map = {
    "python": ".py",
    "javascript": ".js",
    "java": ".java",
    "c++": ".cpp",
    "html": ".html",
    "css": ".css",
    "json": ".json",
    "xml": ".xml",
    "yaml": ".yaml",
    "bash": ".sh",
    "shell": ".sh",
}

def get_extension(prompt: str) -> str:
    prompt = prompt.lower()
    for keyword, ext in extension_map.items():
        if keyword in prompt:
            return ext
    return ".txt"  # default fallback

def extract_code_blocks(text: str) -> str:
    # Extract content inside triple backticks optionally followed by language
    matches = re.findall(r"```(?:[\w\+\#]*)\n(.*?)```", text, re.DOTALL)
    if matches:
        return "\n\n".join(matches).strip()
    else:
        # Fallback: return the whole response if no code block is found
        return text.strip()

# Create the coding agent
agent = Agent(
    model=Gemini(id="gemini-2.0-flash-exp"),
    instructions=dedent("""\
        You are a versatile coding assistant capable of generating code in any programming language or file format.

        Your responsibilities include:
        - Writing clean, well-documented code
        - Supporting languages like Python, JavaScript, Java, C++, and more
        - Generating structured files like HTML, XML, JSON, YAML, etc.
        - Providing concise explanations or comments when needed
        - Following best practices and adhering to language-specific conventions

        Always ensure the generated code is syntactically correct and ready to use.\
        Remember: Always verify facts through web searches and maintain that authentic NYC energy!
    """),
    tools=[DuckDuckGoTools()],
    show_tool_calls=True,
    markdown=True,
)

# User prompt
prompt = "What is today's date"

# Get agent response
response = agent.run(prompt)

# Extract code from the response
code_only = extract_code_blocks(response.content)

# Determine file extension
ext = get_extension(prompt)
filename = f"generated_code{ext}"

# Save just the code to the file
with open(filename, "w", encoding="utf-8") as f:
    f.write(code_only)

print(f"✅ Code extracted and saved to {filename}")
